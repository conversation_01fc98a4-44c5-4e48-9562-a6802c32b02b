import type { User } from '@supabase/supabase-js';
import { supabase } from '../supabase/client';
import { backendAPI } from './backendService';
import type {
  PaymentData,
  PaymentResponse,
  PaymentResult,
  ServiceFormData
} from '../../types/forms';

// Use the API URL from environment variables
const _API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

// Check if Square is properly configured
export function isSquareConfigured(): boolean {
  const applicationId = import.meta.env.VITE_SQUARE_APPLICATION_ID;
  const locationId = import.meta.env.VITE_SQUARE_LOCATION_ID;
  return !!(applicationId && locationId && applicationId !== 'YOUR_SQUARE_APPLICATION_ID');
}

// Process a direct payment (for future use with embedded forms)
export async function processDirectPayment(
  token: string,
  amount: number,
  user?: User,
  customerInfo?: {
    email?: string;
    name?: string;
    phone?: string;
  }
): Promise<PaymentResult> {
  try {
    // This would be used for direct card payments
    // For now, redirect to createPaymentLink
    const result = await createPaymentLink({
      amount,
      customerEmail: customerInfo?.email || user?.email,
      customerName: customerInfo?.name,
      description: 'Empire Pro Service',
    });

    return {
      success: result.success,
      error: result.error,
      payment: result.paymentLink,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Payment processing failed',
    };
  }
}

export async function processResidentialPayment(
  formData: ServiceFormData,
  amount: number,
  user: User | null
): Promise<PaymentResponse> {
  try {
    // Processing residential payment

    // Check if Square is configured
    if (!isSquareConfigured()) {
      // Use alternative backend API
      const apiUrl = import.meta.env.VITE_API_URL 
        ? `${import.meta.env.VITE_API_URL}/api/create-payment`
        : process.env.NODE_ENV === 'development' 
          ? 'http://localhost:3001/api/create-payment' // Express server
          : '/api/create-payment'; // Vercel/Netlify functions

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          currency: 'USD',
          description: `${formData.serviceType ? formData.serviceType.charAt(0).toUpperCase() + formData.serviceType.slice(1) : 'Residential'} Cleaning Service`,
          customerEmail: user?.email || formData.contact?.email,
          orderId: formData.orderId,
        }),
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Failed to create payment link');
      }

      // Store payment record in database if user is authenticated
      if (user && data.paymentLink) {
        try {
          await supabase
            .from('leads')
            .insert({
              user_id: user.id,
              service_type: formData.serviceType || 'residential',
              name: formData.contact?.name || user.email,
              email: user.email,
              phone: formData.contact?.phone,
              status: 'pending_payment',
              lead_source: 'website',
              estimated_value: amount,
              metadata: {
                form_data: formData,
                payment_link: data.paymentLink.url,
                payment_link_id: data.paymentLink.id,
              }
            });
        } catch {
          // Error storing lead - continue with payment
        }
      }

      return {
        success: true,
        paymentLink: data.paymentLink,
        paymentId: data.paymentLink.id,
      };
    }

    // Original Supabase Edge Function code (kept as fallback)
    const { data, error } = await supabase.functions.invoke('create-payment-link', {
      body: {
        amount: Math.round(amount * 100), // Convert to cents for Square API
        currency: 'USD',
        description: `${formData.serviceType ? formData.serviceType.charAt(0).toUpperCase() + formData.serviceType.slice(1) : 'Residential'} Cleaning Service`,
        customerEmail: user?.email || formData.contact?.email,
        formData: formData
      }
    });

    if (error) {
      throw new Error(error.message || 'Failed to create payment link');
    }

    if (!data?.paymentLink?.url) {
      throw new Error('Invalid response from payment service');
    }

    // Store payment record in database if user is authenticated
    if (user) {
      try {
        const { error: dbError } = await supabase
          .from('payment_records')
          .insert({
            user_id: user.id,
            service_type: formData.serviceType || 'residential',
            amount: amount,
            payment_link_url: data.paymentLink.url,
            payment_link_id: data.paymentLink.id,
            status: 'pending',
            metadata: {
              form_data: formData
            }
          });

        if (dbError) {
          // Continue even if storage fails
        }
      } catch {
        // Continue even if storage fails
      }
    }

    return {
      success: true,
      paymentLink: data.paymentLink,
      paymentId: data.paymentLink.id,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to process payment',
    };
  }
}

// Update payment status
export async function updatePaymentStatus(paymentLinkId: string, status: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('payment_records')
      .update({ status, updated_at: new Date() })
      .eq('payment_link_id', paymentLinkId);
      
    if (error) throw error;
    
    return true;
  } catch (error) {
    return false;
  }
}

export async function createPaymentLink(data: PaymentData): Promise<PaymentResponse> {
  try {
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    
    // Use the integrated backend API
    const result = await backendAPI.payments.createPayment({
      amount: data.amount,
      currency: data.currency || 'USD',
      description: data.description || 'Empire Pro Service',
      customerEmail: data.customerEmail || user?.email || '',
      customerName: data.customerName,
      serviceType: data.serviceType,
      formData: data.metadata,
      userId: user?.id,
    });

    return {
      success: result.success,
      paymentLink: result.paymentLink,
      paymentId: result.paymentId,
      error: result.error,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create payment link',
    };
  }
}

export async function checkPaymentStatus(paymentId: string): Promise<unknown> {
  try {
    const result = await backendAPI.payments.getPayment(paymentId);
    return result.payment;
  } catch {
    return null;
  }
}

export async function getUserPayments(userId: string, options?: {
  status?: string;
  limit?: number;
  offset?: number;
}): Promise<unknown[]> {
  try {
    const result = await backendAPI.payments.getUserPayments(userId, options);
    return result.payments || [];
  } catch {
    return [];
  }
}