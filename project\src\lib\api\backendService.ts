import { supabase } from '../supabase/client';
import type { ServiceFormData, ApiResponse } from '../../types/forms';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

// Helper function to get auth headers
async function getAuthHeaders() {
  const { data: { session } } = await supabase.auth.getSession();
  return {
    'Content-Type': 'application/json',
    'Authorization': session?.access_token ? `Bearer ${session.access_token}` : '',
  };
}

// Payment Services
export const paymentService = {
  async createPayment(data: {
    amount: number;
    currency?: string;
    description: string;
    customerEmail: string;
    customerName?: string;
    serviceType?: string;
    formData?: ServiceFormData;
    userId?: string;
  }): Promise<ApiResponse> {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_URL}/api/create-payment`, {
      method: 'POST',
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to create payment');
    }

    return response.json();
  },

  async getPayment(paymentId: string) {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_URL}/api/payment/${paymentId}`, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to fetch payment');
    }

    return response.json();
  },

  async getUserPayments(userId: string, options?: {
    status?: string;
    limit?: number;
    offset?: number;
  }) {
    const headers = await getAuthHeaders();
    const params = new URLSearchParams();
    
    if (options?.status) params.append('status', options.status);
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.offset) params.append('offset', options.offset.toString());

    const response = await fetch(`${API_URL}/api/payments/user/${userId}?${params}`, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to fetch payments');
    }

    return response.json();
  },
};

// Booking Services
export const bookingService = {
  async createBooking(booking: ServiceFormData): Promise<ApiResponse> {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_URL}/api/bookings`, {
      method: 'POST',
      headers,
      body: JSON.stringify(booking),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to create booking');
    }

    return response.json();
  },

  async getUserBookings(userId: string, serviceType?: string) {
    const headers = await getAuthHeaders();
    const params = serviceType ? `?serviceType=${serviceType}` : '';
    
    const response = await fetch(`${API_URL}/api/bookings/user/${userId}${params}`, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to fetch bookings');
    }

    return response.json();
  },
};

// Analytics Services
export const analyticsService = {
  async getSummary() {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_URL}/api/analytics/summary`, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to fetch analytics');
    }

    return response.json();
  },
};

// Health Check
export const healthService = {
  async checkHealth() {
    try {
      const response = await fetch(`${API_URL}/health`, {
        method: 'GET',
      });

      if (!response.ok) {
        return { status: 'error', services: {} };
      }

      return response.json();
    } catch {
      return { status: 'error', services: {} };
    }
  },
};

// Combined service export
export const backendAPI = {
  payments: paymentService,
  bookings: bookingService,
  analytics: analyticsService,
  health: healthService,
}; 