<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #loading {
            display: none;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>🔧 Payment System Debug Test</h1>
    
    <div class="test-section">
        <h2>Test Payment Creation</h2>
        <p>This will create a test payment and show you exactly what's happening.</p>
        
        <button onclick="testPayment()">Create Test Payment ($100)</button>
        
        <div id="loading">⏳ Processing...</div>
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>Quick Checks</h2>
        <button onclick="checkHealth()">Check Backend Health</button>
        <button onclick="checkFrontend()">Check Frontend Connection</button>
        <div id="quickResult" class="result" style="display: none;"></div>
    </div>

    <script>
        async function testPayment() {
            const resultDiv = document.getElementById('result');
            const loadingDiv = document.getElementById('loading');
            
            resultDiv.style.display = 'none';
            loadingDiv.style.display = 'block';
            
            try {
                console.log('Sending payment request...');
                
                const response = await fetch('http://localhost:3001/api/create-payment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        amount: 100,
                        currency: 'USD',
                        description: 'Test Payment',
                        customerEmail: '<EMAIL>',
                        customerName: 'Test User',
                        serviceType: 'test'
                    })
                });
                
                const data = await response.json();
                
                loadingDiv.style.display = 'none';
                resultDiv.style.display = 'block';
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ SUCCESS! Payment link created!

Response:
${JSON.stringify(data, null, 2)}

Payment Link URL: ${data.paymentLink?.url || 'Not found'}
Payment Link ID: ${data.paymentLink?.id || 'Not found'}

Click here to test the payment:
<a href="${data.paymentLink?.url}" target="_blank">${data.paymentLink?.url}</a>`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ PAYMENT FAILED

Error: ${data.error}

Full Response:
${JSON.stringify(data, null, 2)}

Common Issues:
1. Square credentials not set correctly in server/.env
2. Invalid location ID (must match your Square account)
3. Square account not activated for sandbox
4. Backend server needs restart after adding credentials`;
                }
            } catch (error) {
                loadingDiv.style.display = 'none';
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ NETWORK ERROR

Error: ${error.message}

This usually means:
- Backend server is not running on port 3001
- CORS issue (shouldn't happen with local setup)

Check:
1. Is backend running? Look for PowerShell window with "Empire Pro Backend Server"
2. Any errors in the backend console?`;
            }
        }
        
        async function checkHealth() {
            const resultDiv = document.getElementById('quickResult');
            try {
                const response = await fetch('http://localhost:3001/health');
                const data = await response.json();
                
                resultDiv.style.display = 'block';
                resultDiv.className = 'result info';
                resultDiv.innerHTML = `Backend Health Check:
${JSON.stringify(data, null, 2)}

Square Configured: ${data.services?.square ? '✅ Yes' : '❌ No'}
Supabase Connected: ${data.services?.supabase ? '✅ Yes' : '❌ No'}`;
            } catch (error) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `Backend is not responding: ${error.message}`;
            }
        }
        
        async function checkFrontend() {
            const resultDiv = document.getElementById('quickResult');
            try {
                const response = await fetch('http://localhost:5173');
                resultDiv.style.display = 'block';
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `Frontend is running! Status: ${response.status}`;
            } catch (error) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `Frontend is not running: ${error.message}`;
            }
        }
        
        // Check health on load
        window.onload = () => {
            checkHealth();
        };
    </script>
</body>
</html> 