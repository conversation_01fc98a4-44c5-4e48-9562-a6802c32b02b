import { supabase, isSupabaseConfigured } from '../supabase/client';
import type { User } from '@supabase/supabase-js';

export interface LeadData {
  full_name: string;
  email: string;
  phone: string;
  company_name?: string;
  zip_code: string;
  square_footage?: number;
  service_interests: string[];
  service_frequency?: string;
  preferred_start_date?: string;
  preferred_contact_method?: string;
  notes?: string;
  property_type?: string;
  industry_type?: string;
  special_requirements?: string[];
  custom_requests?: string;
}

export async function submitLead(leadData: LeadData, user: User | null) {
  try {
    if (!isSupabaseConfigured) {
      throw new Error('Database connection not configured. Please connect to Supabase first.');
    }

    const { data, error } = await supabase
      .from('leads')
      .insert([{
        ...leadData,
        created_by: user?.id || null,
        status: 'new',
        source: 'website'
      }])
      .select()
      .single();

    if (error) {
      console.error('Error submitting lead:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in submitLead:', error);
    throw error;
  }
}

export async function getLeads(user: User) {
  try {
    if (!isSupabaseConfigured) {
      throw new Error('Database connection not configured. Please connect to Supabase first.');
    }

    const { data, error } = await supabase
      .from('leads')
      .select(`
        *,
        lead_activities (*),
        lead_documents (*)
      `)
      .or(`created_by.eq.${user.id},assigned_to.eq.${user.id}`)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching leads:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in getLeads:', error);
    throw error;
  }
}

export async function updateLead(leadId: string, updates: Partial<LeadData>, _user: User) {
  try {
    if (!isSupabaseConfigured) {
      throw new Error('Database connection not configured. Please connect to Supabase first.');
    }

    const { data, error } = await supabase
      .from('leads')
      .update(updates)
      .eq('id', leadId)
      .select()
      .single();

    if (error) {
      console.error('Error updating lead:', error);
      throw error;
    }

    // Log the activity
    await supabase.rpc('log_lead_activity', {
      p_lead_id: leadId,
      p_activity_type: 'update',
      p_description: 'Lead information updated'
    });

    return data;
  } catch (error) {
    console.error('Error in updateLead:', error);
    throw error;
  }
}

export async function addLeadActivity(
  leadId: string,
  activityType: string,
  description: string,
  _user: User
) {
  try {
    if (!isSupabaseConfigured) {
      throw new Error('Database connection not configured. Please connect to Supabase first.');
    }

    const { data, error } = await supabase.rpc('log_lead_activity', {
      p_lead_id: leadId,
      p_activity_type: activityType,
      p_description: description
    });

    if (error) {
      console.error('Error adding lead activity:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in addLeadActivity:', error);
    throw error;
  }
}