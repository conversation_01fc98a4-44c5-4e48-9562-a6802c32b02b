# 🧹 Codebase Cleanup Plan

## Current Issues:
1. ✅ Multiple duplicate Supabase folders (archived)
2. ✅ Test files, batch scripts, and docs scattered in project root (organized)
3. ✅ Unrelated cli-develop folder taking up space (archived)
4. ✅ Empty src/ folder at root (removed)
5. ✅ Executable files at root level (moved to _executables)

## New Structure:
```
ep official/
├── project/                    # Main application
│   ├── src/                   # Source code
│   ├── public/                # Public assets
│   ├── server/                # Backend server
│   ├── _tools/                # Development tools
│   │   ├── scripts/           # Batch files & setup scripts
│   │   └── tests/             # Test HTML & JS files
│   └── _docs/                 # Documentation
├── _archive/                  # Archived/unused folders
│   ├── cli-develop/
│   ├── supabase-cli/
│   ├── square-mcp-server/
│   └── supabase-mcp-server/
└── _executables/              # Supabase executables
```

## Cleanup Steps:
- [x] Create new directory structure
- [x] Move test files to _tools/tests
- [x] Move batch scripts to _tools/scripts
- [x] Move documentation to _docs
- [x] Archive unused folders
- [x] Clean up root directory
- [x] Update .gitignore
- [x] Create proper README
- [x] Create start script

## What's Been Done:
1. **Organized project folder** - All test files, scripts, and docs are now properly categorized
2. **Archived unused folders** - cli-develop and MCP servers moved to _archive
3. **Cleaned root directory** - Only essential folders remain at root
4. **Created documentation** - New README with clear structure and instructions
5. **Added convenience script** - start.bat to easily run both servers

## Remaining Items:
- The `supabase/` folder at root (kept as it may be needed)
- The `package-lock.json` at root (minimal, can be removed if not needed) 