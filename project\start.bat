@echo off
echo ============================================
echo    🚀 Starting Empire Pro Application
echo ============================================
echo.

echo 📦 Checking dependencies...
if not exist node_modules (
    echo Installing dependencies...
    call npm install
    echo.
)

echo 🔧 Checking environment configuration...
if not exist .env (
    echo ⚠️  No .env file found!
    echo Please create .env file with your credentials
    echo See _docs folder for setup guides
    pause
    exit /b 1
)

echo.
echo 🌐 Starting frontend server...
start cmd /k "npm run dev"

echo.
echo 💳 Starting payment server...
start cmd /k "cd server && npm start"

echo.
echo ============================================
echo ✅ Both servers are starting!
echo.
echo Frontend: http://localhost:5173
echo Backend:  http://localhost:3001
echo.
echo Press any key to exit this window...
echo ============================================
pause >nul 