import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Validate environment variables
const isValidUrl = supabaseUrl && supabaseUrl.startsWith('https://') && supabaseUrl.includes('.supabase.co');
const isValidKey = supabaseAnonKey && supabaseAnonKey.length > 50; // JWT tokens are typically longer

// Check if environment variables are properly configured
export const isSupabaseConfigured = Boolean(isValidUrl && isValidKey);

if (!isSupabaseConfigured) {
  console.error('Supabase configuration is missing or invalid.');
  console.error('URL valid:', !!isValidUrl);
  console.error('Key valid:', !!isValidKey);
  console.error('Please check your environment variables in .env file.');
}

// Create and export the Supabase client with proper configuration
export const supabase = isSupabaseConfigured
  ? createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true
      },
      realtime: {
        params: {
          eventsPerSecond: 10
        }
      }
    })
  : null;

// Helper function to check if client is available
export function getSupabaseClient() {
  if (!supabase) {
    throw new Error(
      'Supabase client is not configured. Please check your environment variables and ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set correctly.'
    );
  }
  return supabase;
}

// Helper function to check network connectivity with Supabase
export async function checkSupabaseConnection(): Promise<{ success: boolean; error?: string; details?: string }> {
  if (!isSupabaseConfigured) {
    return {
      success: false,
      error: 'Supabase is not configured',
      details: 'Please check your environment variables in .env file'
    };
  }

  try {
    const client = getSupabaseClient();

    // Test basic connectivity with a simple query
    const { error } = await client
      .from('profiles')
      .select('id')
      .limit(1);

    if (error) {
      // Check for specific error types
      if (error.message.includes('Invalid API key')) {
        return {
          success: false,
          error: 'Invalid API key',
          details: 'The Supabase anon key is invalid or expired. Please check your .env file.'
        };
      } else if (error.message.includes('relation "profiles" does not exist')) {
        return {
          success: false,
          error: 'Database table missing',
          details: 'The profiles table does not exist. Please run database migrations.'
        };
      } else {
        return {
          success: false,
          error: 'Database error',
          details: error.message
        };
      }
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: 'Connection failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}