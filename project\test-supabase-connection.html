<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Supabase Connection</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
        }
        button {
            background: #3ecf8e;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #2ba975;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
        .credentials {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test Supabase Connection</h1>
        
        <div class="test-section">
            <h2>Current Credentials:</h2>
            <div class="credentials">
                <strong>URL:</strong> https://auyztjlijlbyopxrnxqz.supabase.co<br>
                <strong>Anon Key:</strong> eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF1eXp0amxpamxieW9weHJueHF6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzYwNDAxMDksImV4cCI6MjA1MTYxNjEwOX0._0PvItjCjDHHxwOZD5vAPfp2l2J99v8VBEHK7UDcIF8Q
            </div>
        </div>

        <div class="test-section">
            <h2>Connection Tests:</h2>
            <button onclick="runTests()">Run All Tests</button>
            <div id="results" style="margin-top: 20px;"></div>
        </div>

        <div class="test-section">
            <h2>Test Custom Credentials:</h2>
            <p>If the above tests fail, try with your current credentials from Supabase dashboard:</p>
            <input type="text" id="customUrl" placeholder="Your Supabase URL" style="width: 100%; padding: 8px; margin: 5px 0;">
            <input type="text" id="customKey" placeholder="Your anon key" style="width: 100%; padding: 8px; margin: 5px 0;">
            <button onclick="testCustom()">Test Custom Credentials</button>
            <div id="customResults" style="margin-top: 20px;"></div>
        </div>
    </div>

    <script>
        const SUPABASE_URL = 'https://auyztjlijlbyopxrnxqz.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF1eXp0amxpamxieW9weHJueHF6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzYwNDAxMDksImV4cCI6MjA1MTYxNjEwOX0._0PvItjCjDHHxwOZD5vAPfp2l2J99v8VBEHK7UDcIF8Q';

        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message;
            results.appendChild(div);
        }

        async function runTests() {
            document.getElementById('results').innerHTML = '';
            
            addResult('Starting tests...', 'info');
            
            // Test 1: Initialize client
            try {
                const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
                addResult('✅ Test 1: Client initialized successfully', 'success');
                
                // Test 2: Check auth
                try {
                    const { data: { session }, error } = await supabase.auth.getSession();
                    if (error) {
                        addResult(`❌ Test 2: Auth check failed - ${error.message}`, 'error');
                        addResult(`Error details: ${JSON.stringify(error)}`, 'error');
                    } else {
                        addResult('✅ Test 2: Auth service is accessible', 'success');
                    }
                } catch (e) {
                    addResult(`❌ Test 2: Auth error - ${e.message}`, 'error');
                }
                
                // Test 3: Try a simple query
                try {
                    const { data, error } = await supabase
                        .from('test_table_that_does_not_exist')
                        .select('*')
                        .limit(1);
                    
                    if (error) {
                        if (error.message.includes('relation') && error.message.includes('does not exist')) {
                            addResult('✅ Test 3: Database connection works (table doesn\'t exist, which is expected)', 'success');
                        } else if (error.message.includes('Invalid API key')) {
                            addResult('❌ Test 3: Invalid API key error!', 'error');
                            addResult('Your API key is being rejected by Supabase', 'error');
                        } else {
                            addResult(`❌ Test 3: Database query failed - ${error.message}`, 'error');
                        }
                    } else {
                        addResult('✅ Test 3: Database query successful', 'success');
                    }
                } catch (e) {
                    addResult(`❌ Test 3: Query error - ${e.message}`, 'error');
                }
                
                // Test 4: Check project status
                try {
                    const response = await fetch(`${SUPABASE_URL}/rest/v1/`, {
                        headers: {
                            'apikey': SUPABASE_ANON_KEY,
                            'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                        }
                    });
                    
                    if (response.status === 401) {
                        addResult('❌ Test 4: 401 Unauthorized - API key is invalid', 'error');
                    } else if (response.status === 404) {
                        addResult('✅ Test 4: API endpoint accessible (404 is normal for root path)', 'success');
                    } else {
                        addResult(`ℹ️ Test 4: API returned status ${response.status}`, 'info');
                    }
                } catch (e) {
                    addResult(`❌ Test 4: Network error - ${e.message}`, 'error');
                }
                
            } catch (e) {
                addResult(`❌ Failed to initialize client: ${e.message}`, 'error');
            }
            
            addResult('<br><strong>Tests complete!</strong>', 'info');
        }

        async function testCustom() {
            document.getElementById('customResults').innerHTML = '';
            const customUrl = document.getElementById('customUrl').value;
            const customKey = document.getElementById('customKey').value;
            
            if (!customUrl || !customKey) {
                document.getElementById('customResults').innerHTML = '<div class="error">Please enter both URL and key</div>';
                return;
            }
            
            try {
                const supabase = window.supabase.createClient(customUrl, customKey);
                const { data: { session }, error } = await supabase.auth.getSession();
                
                if (error && error.message.includes('Invalid API key')) {
                    document.getElementById('customResults').innerHTML = '<div class="error">❌ Invalid API key with these credentials too!</div>';
                } else {
                    document.getElementById('customResults').innerHTML = '<div class="success">✅ These credentials work! Update your .env file with these values.</div>';
                }
            } catch (e) {
                document.getElementById('customResults').innerHTML = `<div class="error">❌ Error: ${e.message}</div>`;
            }
        }
    </script>
</body>
</html> 