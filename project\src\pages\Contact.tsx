import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Send,
  Clock,
  Phone, Mail, MapPin, ArrowRight,
  Shield, Star, Users
} from 'lucide-react';
import { Header } from '../components/layout/Header';
import { Footer } from '../components/layout/Footer';
import { Button } from '../components/ui/Button';

export function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    companyName: '',
    serviceInterest: '',
    message: ''
  });
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitStatus('loading');
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSubmitStatus('success');
      setFormData({
        name: '',
        email: '',
        phone: '',
        companyName: '',
        serviceInterest: '',
        message: ''
      });
    } catch {
      setSubmitStatus('error');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      {/* Enhanced Hero Section with increased top padding */}
      <section className="relative pt-40 sm:pt-48 pb-24 bg-gradient-to-br from-brand-600 to-brand-700 overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute inset-0">
            {/* Wave 1 */}
            <div className="absolute bottom-0 left-0 right-0 h-32 opacity-20">
              <div className="absolute inset-0 w-[200%] animate-wave">
                <svg viewBox="0 0 100 100" preserveAspectRatio="none" className="w-full h-full fill-current text-white">
                  <path d="M0,50 Q25,60 50,50 Q75,40 100,50 L100,100 L0,100 Z" />
                </svg>
              </div>
            </div>
            {/* Wave 2 */}
            <div className="absolute bottom-0 left-0 right-0 h-32 opacity-10">
              <div className="absolute inset-0 w-[200%] animate-wave-slow">
                <svg viewBox="0 0 100 100" preserveAspectRatio="none" className="w-full h-full fill-current text-white">
                  <path d="M0,60 Q25,50 50,60 Q75,70 100,60 L100,100 L0,100 Z" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="inline-flex items-center justify-center p-3 bg-white/10 backdrop-blur-sm rounded-xl mb-6"
            >
              <Send className="w-6 h-6 text-white" />
            </motion.div>
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="text-4xl font-bold text-white mb-6"
            >
              Get in Touch
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-xl text-brand-100 max-w-2xl mx-auto"
            >
              Have questions? We're here to help. Contact us for immediate assistance.
            </motion.p>

            {/* Quick Contact Options */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mt-12 flex flex-wrap justify-center gap-4"
            >
              <a
                href="tel:+17187171502"
                className="inline-flex items-center px-6 py-3 rounded-xl bg-white/10 backdrop-blur-sm 
                         hover:bg-white/20 transition-colors text-white"
              >
                <Phone className="w-5 h-5 mr-2" />
                (*************
              </a>
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center px-6 py-3 rounded-xl bg-white/10 backdrop-blur-sm 
                         hover:bg-white/20 transition-colors text-white"
              >
                <Mail className="w-5 h-5 mr-2" />
                Email Us
              </a>
              <a
                href="#quote"
                className="inline-flex items-center px-6 py-3 rounded-xl bg-white text-brand-600 
                         hover:bg-brand-50 transition-colors"
              >
                Get a Quote
                <ArrowRight className="w-5 h-5 ml-2" />
              </a>
            </motion.div>
          </div>
        </div>
      </section>

      <main className="py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="bg-white rounded-2xl shadow-xl p-8"
            >
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                Send Us a Message
              </h2>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Full Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Company Name
                    </label>
                    <input
                      type="text"
                      value={formData.companyName}
                      onChange={(e) => setFormData({ ...formData, companyName: e.target.value })}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Phone Number <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Service Interest
                  </label>
                  <select
                    value={formData.serviceInterest}
                    onChange={(e) => setFormData({ ...formData, serviceInterest: e.target.value })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                  >
                    <option value="">Select a service</option>
                    <option value="office">Office Cleaning</option>
                    <option value="carpet">Carpet Cleaning</option>
                    <option value="window">Window Cleaning</option>
                    <option value="deep">Deep Cleaning</option>
                    <option value="sanitization">Sanitization</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Message <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    value={formData.message}
                    onChange={(e) => setFormData({ ...formData, message: e.target.value })}
                    rows={4}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                    required
                  />
                </div>

                <Button
                  type="submit"
                  disabled={submitStatus === 'loading'}
                  className="w-full"
                >
                  {submitStatus === 'loading' ? 'Sending...' : 'Send Message'}
                </Button>

                {submitStatus !== 'idle' && (
                  <div className={`p-4 rounded-lg ${
                    submitStatus === 'success' ? 'bg-green-50' : 'bg-red-50'
                  }`}>
                    <p className={submitStatus === 'success' ? 'text-green-700' : 'text-red-700'}>
                      {submitStatus === 'success'
                        ? 'Message sent successfully! We will get back to you soon.'
                        : 'There was an error sending your message. Please try again.'}
                    </p>
                  </div>
                )}
              </form>
            </motion.div>

            {/* Contact Information */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="space-y-8"
            >
              {/* Main Office */}
              <div className="bg-white rounded-2xl shadow-xl p-8">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="p-3 rounded-xl bg-brand-100">
                    <MapPin className="w-6 h-6 text-brand-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900">Main Office</h3>
                    <p className="text-gray-600">Long Island City, New York</p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <MapPin className="w-5 h-5 text-brand-600" />
                    <div>
                      <p className="text-gray-900 font-medium">Address</p>
                      <p className="text-gray-600">47-38 Vernon Blvd</p>
                      <p className="text-gray-600">Long Island City, NY 11101</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Clock className="w-5 h-5 text-brand-600" />
                    <div>
                      <p className="text-gray-900 font-medium">Hours of Operation</p>
                      <p className="text-gray-600">Monday - Sunday: 8:00 AM - 10:00 PM EST</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="w-5 h-5 text-brand-600" />
                    <div>
                      <p className="text-gray-900 font-medium">Phone</p>
                      <p className="text-gray-600">(*************</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Mail className="w-5 h-5 text-brand-600" />
                    <div>
                      <p className="text-gray-900 font-medium">Email</p>
                      <p className="text-gray-600"><EMAIL></p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Trust Badges */}
              <div className="bg-white rounded-2xl shadow-xl p-8">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">Why Choose Us</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-xl">
                    <Shield className="w-5 h-5 text-brand-600" />
                    <div className="text-sm">
                      <p className="font-medium text-gray-900">Licensed & Insured</p>
                      <p className="text-gray-600">Fully bonded</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-xl">
                    <Star className="w-5 h-5 text-brand-600" />
                    <div className="text-sm">
                      <p className="font-medium text-gray-900">4.9/5 Rating</p>
                      <p className="text-gray-600">500+ Reviews</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-xl">
                    <Users className="w-5 h-5 text-brand-600" />
                    <div className="text-sm">
                      <p className="font-medium text-gray-900">Expert Team</p>
                      <p className="text-gray-600">Trained staff</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-xl">
                    <Clock className="w-5 h-5 text-brand-600" />
                    <div className="text-sm">
                      <p className="font-medium text-gray-900">24/7 Service</p>
                      <p className="text-gray-600">Always available</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Connect With Us */}
              <div className="bg-white rounded-2xl shadow-xl p-8">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">Connect With Us</h3>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <a
                    href="https://www.facebook.com/empireprocleanings/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-center p-4 rounded-xl bg-blue-50 hover:bg-blue-100 transition-colors"
                  >
                    <span className="text-blue-600 font-medium">Facebook</span>
                  </a>
                  <a
                    href="https://www.instagram.com/empireprocleaning/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-center p-4 rounded-xl bg-pink-50 hover:bg-pink-100 transition-colors"
                  >
                    <span className="text-pink-600 font-medium">Instagram</span>
                  </a>
                  <a
                    href="https://maps.app.goo.gl/zojAYuPCMPD3Cnv78"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-center p-4 rounded-xl bg-red-50 hover:bg-red-100 transition-colors"
                  >
                    <span className="text-red-600 font-medium">Google Business</span>
                  </a>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}