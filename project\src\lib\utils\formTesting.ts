import type { ServiceFormData } from '../../types/forms';
import { saveFormData, loadFormData, clearFormData } from './formStorage';

/**
 * Test form data persistence functionality
 */
export function testFormPersistence(): boolean {
  try {
    // Test data
    const testFormData: ServiceFormData = {
      propertyDetails: {
        propertyType: 'house',
        squareFootage: 2000,
        propertyAddress: '123 Test St',
        bedrooms: 3,
        bathrooms: 2,
        floors: 2
      },
      contact: {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        phone: '555-0123'
      },
      schedule: {
        preferredDate: '2024-01-15',
        preferredTime: 'morning',
        frequency: 'one-time'
      }
    };

    // Test saving
    saveFormData(testFormData, 'test');
    
    // Test loading
    const loadedData = loadFormData();
    
    if (!loadedData) {
      console.error('Form data was not saved or loaded correctly');
      return false;
    }

    // Test clearing
    clearFormData();
    const clearedData = loadFormData();
    
    if (clearedData !== null) {
      console.error('Form data was not cleared correctly');
      return false;
    }

    return true;
  } catch (error) {
    console.error('Form persistence test failed:', error);
    return false;
  }
}

/**
 * Validate form data structure
 */
export function validateFormData(formData: ServiceFormData): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check required fields
  if (!formData.contact?.firstName?.trim()) {
    errors.push('First name is required');
  }

  if (!formData.contact?.lastName?.trim()) {
    errors.push('Last name is required');
  }

  if (!formData.contact?.email?.trim()) {
    errors.push('Email is required');
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.contact.email)) {
    errors.push('Valid email is required');
  }

  if (!formData.contact?.phone?.trim()) {
    errors.push('Phone number is required');
  }

  if (!formData.propertyDetails?.propertyAddress?.trim()) {
    errors.push('Property address is required');
  }

  if (!formData.propertyDetails?.squareFootage || formData.propertyDetails.squareFootage <= 0) {
    errors.push('Valid square footage is required');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Test form validation logic
 */
export function testFormValidation(): boolean {
  try {
    // Test valid form data
    const validFormData: ServiceFormData = {
      propertyDetails: {
        propertyType: 'house',
        squareFootage: 2000,
        propertyAddress: '123 Test St',
        bedrooms: 3,
        bathrooms: 2,
        floors: 2
      },
      contact: {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        phone: '555-0123'
      },
      schedule: {
        preferredDate: '2024-01-15',
        preferredTime: 'morning',
        frequency: 'one-time'
      }
    };

    const validResult = validateFormData(validFormData);
    if (!validResult.isValid) {
      console.error('Valid form data failed validation:', validResult.errors);
      return false;
    }

    // Test invalid form data
    const invalidFormData: ServiceFormData = {
      propertyDetails: {
        propertyType: '',
        squareFootage: 0,
        propertyAddress: '',
        bedrooms: 0,
        bathrooms: 0,
        floors: 0
      },
      contact: {
        firstName: '',
        lastName: '',
        email: 'invalid-email',
        phone: ''
      },
      schedule: {
        preferredDate: '',
        preferredTime: '',
        frequency: ''
      }
    };

    const invalidResult = validateFormData(invalidFormData);
    if (invalidResult.isValid) {
      console.error('Invalid form data passed validation');
      return false;
    }

    return true;
  } catch (error) {
    console.error('Form validation test failed:', error);
    return false;
  }
}

/**
 * Run all form tests
 */
export function runFormTests(): { passed: number; failed: number; results: Record<string, boolean> } {
  const tests = {
    'Form Persistence': testFormPersistence,
    'Form Validation': testFormValidation
  };

  const results: Record<string, boolean> = {};
  let passed = 0;
  let failed = 0;

  for (const [testName, testFunction] of Object.entries(tests)) {
    try {
      const result = testFunction();
      results[testName] = result;
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.error(`Test "${testName}" threw an error:`, error);
      results[testName] = false;
      failed++;
    }
  }

  return { passed, failed, results };
}

/**
 * Check if forms are properly configured
 */
export function checkFormConfiguration(): { isConfigured: boolean; issues: string[] } {
  const issues: string[] = [];

  // Check localStorage availability
  try {
    localStorage.setItem('test', 'test');
    localStorage.removeItem('test');
  } catch {
    issues.push('localStorage is not available');
  }

  // Check if required form components are available
  if (typeof window === 'undefined') {
    issues.push('Window object is not available');
  }

  return {
    isConfigured: issues.length === 0,
    issues
  };
}
