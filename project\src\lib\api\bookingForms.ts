import { supabase, isSupabaseConfigured } from '../supabase/client';
import type { User } from '@supabase/supabase-js';
import type { ServiceFormData } from '../../types/forms';

export async function submitBookingForm(formData: ServiceFormData, serviceType: string, user: User | null) {
  // Check if Supabase is configured and working
  if (!isSupabaseConfigured || !supabase) {
    return submitBookingFormFallback(formData, serviceType, user);
  }

    // Prepare the request data
    const requestData = {
      user_id: user?.id || null,
      service_type: serviceType,
      property_details: formData.propertyDetails || {},
      service_details: getServiceSpecificDetails(formData, serviceType),
      schedule: formData.schedule || {},
      contact: formData.contact || {},
      status: 'pending'
    };

    // Submitting booking form

    // Insert the booking using Supabase client
    const { data, error } = await supabase
      .from('booking_forms')
      .insert([requestData])
      .select();

    if (error) {
      // Handle Supabase error
      
      // Check if the error is related to the http_post function
      if (error.message && (
        error.message.includes('http_post') || 
        error.message.includes('webhook') ||
        error.message.includes('extension "http"')
      )) {
        // The booking was likely created but the webhook notification failed
        // Try to fetch the booking to confirm
        const { data: bookingData, error: fetchError } = await supabase
          .from('booking_forms')
          .select()
          .match({ 
            user_id: user?.id,
            service_type: serviceType
          })
          .order('created_at', { ascending: false })
          .limit(1);
          
        if (!fetchError && bookingData && bookingData.length > 0) {
          // Booking was created successfully despite webhook error
          
          // Try to manually send the webhook
          try {
            // Format the data according to the required format
            const webhookPayload = {
              id: bookingData[0].id,
              status: 'sent',
              service_type: bookingData[0].service_type,
              property_details: bookingData[0].property_details,
              schedule: bookingData[0].schedule,
              contact: bookingData[0].contact,
              created_at: bookingData[0].created_at
            };
            
            const response = await fetch('https://mohhzaman.app.n8n.cloud/webhook/22aa5359-f353-4f62-8558-03d475bdade9', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(webhookPayload)
            });
            
            if (!response.ok) {
              // Manual webhook notification failed
            }
          } catch {
            // Failed to send manual webhook notification
          }
          
          return bookingData[0];
        }
        
        // If we couldn't confirm the booking was created, show a more helpful error
        throw new Error('Your booking was received, but we encountered an issue with our notification system. Your booking is still being processed. You can check your dashboard for updates.');
      }
      
      throw new Error(error.message || 'Failed to submit request');
    }

    if (!data || data.length === 0) {
      throw new Error('No data returned from booking submission');
    }

    // If the booking was created successfully, try to manually send the webhook as a backup
    try {
      // Format the data according to the required format
      const webhookPayload = {
        id: data[0].id,
        status: 'sent',
        service_type: data[0].service_type,
        property_details: data[0].property_details,
        schedule: data[0].schedule,
        contact: data[0].contact,
        created_at: data[0].created_at
      };
      
      const response = await fetch('https://mohhzaman.app.n8n.cloud/webhook/22aa5359-f353-4f62-8558-03d475bdade9', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(webhookPayload)
      });
      
      if (!response.ok) {
        // Backup webhook notification failed
      }
    } catch {
      // Failed to send backup webhook notification
      // Don't throw an error here, as the booking was still created successfully
    }

    return data[0];
}

// Helper function to extract service-specific details based on form type
function getServiceSpecificDetails(formData: ServiceFormData, serviceType: string): Record<string, unknown> {
  const details: Record<string, unknown> = {};

  switch (serviceType) {
    case 'window':
      details.windowDetails = formData.windowDetails || {};
      details.accessDetails = formData.accessDetails || {};
      break;
      
    case 'carpet':
      details.carpetDetails = formData.carpetDetails || {};
      details.stainTreatment = formData.stainTreatment || {};
      break;
      
    case 'residential-carpet':
      details.carpetDetails = formData.carpetDetails || {};
      details.stainTreatment = formData.stainTreatment || {};
      break;
      
    case 'sanitization':
      details.protocolDetails = formData.protocolDetails || {};
      details.sanitizationScope = formData.sanitizationScope || {};
      break;
      
    case 'construction':
      details.projectDetails = formData.propertyDetails || {};
      details.cleaningScope = formData.cleaningScope || {};
      details.debrisDetails = formData.debrisDetails || {};
      break;
      
    case 'tile':
      details.tileDetails = formData.tileDetails || {};
      details.groutCondition = formData.groutCondition || {};
      break;
      
    case 'deep':
      details.cleaningScope = formData.cleaningScope || {};
      details.additionalServices = formData.services?.types || [];
      break;
      
    case 'floor':
      details.floorDetails = formData.floorDetails || {};
      details.restorationScope = formData.restorationScope || {};
      break;
      
    case 'pressure':
      details.surfaceDetails = formData.surfaceDetails || {};
      details.pressureScope = formData.pressureScope || {};
      break;
      
    case 'office':
      details.cleaningDetails = formData.cleaningDetails || {};
      details.services = formData.services || {};
      break;
      
    default:
      details.serviceDetails = formData.serviceDetails || {};
  }

  return details;
}

// Fallback function for when Supabase is not available
async function submitBookingFormFallback(formData: ServiceFormData, serviceType: string, user: User | null) {
  // Store in localStorage as backup
  const bookingData = {
    id: crypto.randomUUID(),
    user_id: user?.id || 'anonymous',
    service_type: serviceType,
    property_details: formData.propertyDetails || {},
    service_details: getServiceSpecificDetails(formData, serviceType),
    schedule: formData.schedule || {},
    contact: formData.contact || {},
    status: 'pending',
    created_at: new Date().toISOString(),
    fallback_mode: true
  };

  try {
    // Save to localStorage
    const existingBookings = JSON.parse(localStorage.getItem('fallback_bookings') || '[]');
    existingBookings.push(bookingData);
    localStorage.setItem('fallback_bookings', JSON.stringify(existingBookings));

    // Try to send to webhook directly as backup
    try {
      const response = await fetch('https://mohhzaman.app.n8n.cloud/webhook/22aa5359-f353-4f62-8558-03d475bdade9', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...bookingData,
          status: 'sent_fallback'
        })
      });

      if (response.ok) {
        bookingData.status = 'sent_fallback';
      }
    } catch {
      // Webhook failed, but we still have the local storage backup
    }

    return bookingData;
  } catch {
    throw new Error('Failed to save booking data. Please try again or contact support.');
  }
}