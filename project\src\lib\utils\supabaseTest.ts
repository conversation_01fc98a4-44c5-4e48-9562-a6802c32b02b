import { supabase, isSupabaseConfigured, checkSupabaseConnection } from '../supabase/client';

export interface SupabaseTestResult {
  configured: boolean;
  connected: boolean;
  authenticated: boolean;
  tablesExist: boolean;
  errors: string[];
  details: Record<string, unknown>;
}

/**
 * Comprehensive Supabase connection and configuration test
 */
export async function testSupabaseConnection(): Promise<SupabaseTestResult> {
  const result: SupabaseTestResult = {
    configured: false,
    connected: false,
    authenticated: false,
    tablesExist: false,
    errors: [],
    details: {}
  };

  // Test 1: Configuration
  result.configured = isSupabaseConfigured;
  if (!result.configured) {
    result.errors.push('Supabase is not properly configured. Check environment variables.');
    return result;
  }

  // Test 2: Basic Connection
  try {
    const connectionTest = await checkSupabaseConnection();
    result.connected = connectionTest.success;
    if (!connectionTest.success) {
      result.errors.push(`Connection failed: ${connectionTest.error}`);
      if (connectionTest.details) {
        result.details.connectionError = connectionTest.details;
      }
      return result;
    }
  } catch (error) {
    result.errors.push(`Connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return result;
  }

  // Test 3: Authentication Status
  try {
    if (supabase) {
      const { data: { user }, error } = await supabase.auth.getUser();
      result.authenticated = !error && user !== null;
      result.details.user = user ? { id: user.id, email: user.email } : null;
    }
  } catch (error) {
    result.errors.push(`Auth test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  // Test 4: Database Tables
  try {
    if (supabase) {
      const tables = ['profiles', 'booking_forms', 'payments'];
      const tableResults: Record<string, boolean> = {};
      
      for (const table of tables) {
        try {
          const { error } = await supabase.from(table).select('*').limit(1);
          tableResults[table] = !error;
          if (error && !error.message.includes('relation') && !error.message.includes('does not exist')) {
            // Table exists but there might be permission issues
            tableResults[table] = true;
          }
        } catch {
          tableResults[table] = false;
        }
      }
      
      result.tablesExist = Object.values(tableResults).some(exists => exists);
      result.details.tables = tableResults;
    }
  } catch (error) {
    result.errors.push(`Table test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return result;
}

/**
 * Test Supabase environment variables
 */
export function testSupabaseEnvironment(): { valid: boolean; issues: string[] } {
  const issues: string[] = [];
  
  const url = import.meta.env.VITE_SUPABASE_URL;
  const key = import.meta.env.VITE_SUPABASE_ANON_KEY;
  
  if (!url) {
    issues.push('VITE_SUPABASE_URL is not set');
  } else if (!url.startsWith('https://')) {
    issues.push('VITE_SUPABASE_URL must start with https://');
  } else if (!url.includes('.supabase.co')) {
    issues.push('VITE_SUPABASE_URL must be a valid Supabase URL');
  }
  
  if (!key) {
    issues.push('VITE_SUPABASE_ANON_KEY is not set');
  } else if (key.length < 50) {
    issues.push('VITE_SUPABASE_ANON_KEY appears to be invalid (too short)');
  } else if (!key.startsWith('eyJ')) {
    issues.push('VITE_SUPABASE_ANON_KEY appears to be invalid (not a JWT token)');
  }
  
  return {
    valid: issues.length === 0,
    issues
  };
}

/**
 * Get Supabase project info from URL
 */
export function getSupabaseProjectInfo(): { projectId?: string; region?: string; url?: string } {
  const url = import.meta.env.VITE_SUPABASE_URL;
  if (!url) return {};
  
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname;
    const projectId = hostname.split('.')[0];
    
    return {
      projectId,
      region: 'Unknown',
      url
    };
  } catch {
    return { url };
  }
}

/**
 * Create a new Supabase client with custom configuration
 */
export function createTestSupabaseClient(url?: string, key?: string) {
  const testUrl = url || import.meta.env.VITE_SUPABASE_URL;
  const testKey = key || import.meta.env.VITE_SUPABASE_ANON_KEY;
  
  if (!testUrl || !testKey) {
    throw new Error('URL and key are required');
  }
  
  try {
    // Dynamic import for test client
    const { createClient } = await import('@supabase/supabase-js');
    return createClient(testUrl, testKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
  } catch (error) {
    throw new Error(`Failed to create test client: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Test specific Supabase functionality
 */
export async function testSupabaseFunctionality(): Promise<Record<string, boolean>> {
  const tests: Record<string, boolean> = {};
  
  if (!supabase) {
    return { 'Client Available': false };
  }
  
  // Test auth functionality
  try {
    await supabase.auth.getSession();
    tests['Auth Service'] = true;
  } catch {
    tests['Auth Service'] = false;
  }
  
  // Test database functionality
  try {
    await supabase.from('profiles').select('id').limit(1);
    tests['Database Access'] = true;
  } catch {
    tests['Database Access'] = false;
  }
  
  // Test storage functionality
  try {
    const { data } = await supabase.storage.listBuckets();
    tests['Storage Service'] = Array.isArray(data);
  } catch {
    tests['Storage Service'] = false;
  }
  
  return tests;
}

/**
 * Generate Supabase configuration report
 */
export async function generateSupabaseReport(): Promise<string> {
  const envTest = testSupabaseEnvironment();
  const connectionTest = await testSupabaseConnection();
  const functionalityTest = await testSupabaseFunctionality();
  const projectInfo = getSupabaseProjectInfo();
  
  let report = '# Supabase Configuration Report\n\n';
  
  report += '## Environment Variables\n';
  report += `Status: ${envTest.valid ? '✅ Valid' : '❌ Invalid'}\n`;
  if (envTest.issues.length > 0) {
    report += 'Issues:\n';
    envTest.issues.forEach(issue => report += `- ${issue}\n`);
  }
  report += '\n';
  
  report += '## Project Information\n';
  report += `Project ID: ${projectInfo.projectId || 'Unknown'}\n`;
  report += `URL: ${projectInfo.url || 'Not set'}\n\n`;
  
  report += '## Connection Test\n';
  report += `Configured: ${connectionTest.configured ? '✅' : '❌'}\n`;
  report += `Connected: ${connectionTest.connected ? '✅' : '❌'}\n`;
  report += `Authenticated: ${connectionTest.authenticated ? '✅' : '❌'}\n`;
  report += `Tables Exist: ${connectionTest.tablesExist ? '✅' : '❌'}\n`;
  
  if (connectionTest.errors.length > 0) {
    report += '\nErrors:\n';
    connectionTest.errors.forEach(error => report += `- ${error}\n`);
  }
  report += '\n';
  
  report += '## Functionality Test\n';
  Object.entries(functionalityTest).forEach(([test, passed]) => {
    report += `${test}: ${passed ? '✅' : '❌'}\n`;
  });
  
  return report;
}
