<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase Project Status Check</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #3ecf8e;
            padding-bottom: 10px;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .step {
            background: #f8f9fa;
            border-left: 4px solid #3ecf8e;
            padding: 15px;
            margin: 15px 0;
        }
        .button {
            display: inline-block;
            background: #3ecf8e;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .button:hover {
            background: #2ba975;
        }
        .code {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            word-break: break-all;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 Fix Your Supabase "Invalid API Key" Error</h1>
        
        <div class="error">
            <strong>Error:</strong> Invalid API key - Your Supabase project is likely PAUSED!
        </div>

        <h2>📊 Project Details:</h2>
        <div class="code">
            Project ID: auyztjlijlbyopxrnxqz<br>
            Project URL: https://auyztjlijlbyopxrnxqz.supabase.co
        </div>

        <h2>🔧 How to Fix This (2 minutes):</h2>

        <div class="step">
            <h3>Step 1: Check if your project is paused</h3>
            <a href="https://app.supabase.com/projects" target="_blank" class="button">
                Open Supabase Dashboard →
            </a>
            <p>Look for your project. If it shows "Paused", you need to resume it.</p>
        </div>

        <div class="step">
            <h3>Step 2: Resume your project</h3>
            <p>If your project is paused:</p>
            <ol>
                <li>Click on your project card</li>
                <li>Click the <strong>"Resume Project"</strong> button</li>
                <li>Wait 1-2 minutes for it to start up</li>
            </ol>
        </div>

        <div class="step">
            <h3>Step 3: If you can't see your project</h3>
            <p>Try this direct link to your project:</p>
            <a href="https://app.supabase.com/project/auyztjlijlbyopxrnxqz" target="_blank" class="button">
                Open Your Project Directly →
            </a>
        </div>

        <div class="warning">
            <h3>⚠️ Important Notes:</h3>
            <ul>
                <li><strong>Free tier projects pause after 1 week of inactivity</strong></li>
                <li>You can resume them at any time for free</li>
                <li>Your data is NOT lost when paused</li>
                <li>It takes 1-2 minutes to resume after clicking the button</li>
            </ul>
        </div>

        <h2>✅ After Resuming:</h2>
        <div class="success">
            <ol>
                <li>Wait 1-2 minutes for the project to fully start</li>
                <li>Refresh your website (Ctrl+F5)</li>
                <li>Try signing in again - it should work!</li>
            </ol>
        </div>

        <h2>🔑 Your Current API Key:</h2>
        <div class="code" style="font-size: 12px;">
            eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************._0PvItjCjDHHxwOZD5vAPfp2l2J99v8VBEHK7UDcIF8Q
        </div>
        <p><em>This key is valid and correctly formatted - the issue is that your project is paused.</em></p>

        <h2>🆘 Still Having Issues?</h2>
        <p>If resuming doesn't work:</p>
        <ol>
            <li>Clear your browser cache</li>
            <li>Try incognito/private browsing mode</li>
            <li>Check if you're logged into the correct Supabase account</li>
            <li>Make sure you have owner/admin access to the project</li>
        </ol>
    </div>
</body>
</html> 