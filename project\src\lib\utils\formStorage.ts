import type { ServiceFormData } from '../../types/forms';

const FORM_DATA_KEY = 'empire_pro_form_data';
const FORM_STEP_KEY = 'empire_pro_form_step';

/**
 * Save form data to localStorage for persistence across sessions
 */
export function saveFormData(formData: ServiceFormData, serviceType?: string): void {
  try {
    const dataToSave = {
      formData,
      serviceType,
      timestamp: Date.now(),
      url: window.location.pathname
    };
    
    localStorage.setItem(FORM_DATA_KEY, JSON.stringify(dataToSave));
  } catch (error) {
    // Silently fail if localStorage is not available
    console.warn('Failed to save form data to localStorage:', error);
  }
}

/**
 * Load saved form data from localStorage
 */
export function loadFormData(): { formData: ServiceFormData; serviceType?: string; url?: string } | null {
  try {
    const savedData = localStorage.getItem(FORM_DATA_KEY);
    if (!savedData) return null;
    
    const parsed = JSON.parse(savedData);
    
    // Check if data is not too old (24 hours)
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    if (Date.now() - parsed.timestamp > maxAge) {
      clearFormData();
      return null;
    }
    
    return {
      formData: parsed.formData,
      serviceType: parsed.serviceType,
      url: parsed.url
    };
  } catch (error) {
    console.warn('Failed to load form data from localStorage:', error);
    return null;
  }
}

/**
 * Clear saved form data from localStorage
 */
export function clearFormData(): void {
  try {
    localStorage.removeItem(FORM_DATA_KEY);
    localStorage.removeItem(FORM_STEP_KEY);
  } catch (error) {
    console.warn('Failed to clear form data from localStorage:', error);
  }
}

/**
 * Save current form step
 */
export function saveFormStep(step: number): void {
  try {
    localStorage.setItem(FORM_STEP_KEY, step.toString());
  } catch (error) {
    console.warn('Failed to save form step to localStorage:', error);
  }
}

/**
 * Load saved form step
 */
export function loadFormStep(): number | null {
  try {
    const savedStep = localStorage.getItem(FORM_STEP_KEY);
    return savedStep ? parseInt(savedStep, 10) : null;
  } catch (error) {
    console.warn('Failed to load form step from localStorage:', error);
    return null;
  }
}

/**
 * Check if there is saved form data
 */
export function hasSavedFormData(): boolean {
  try {
    return localStorage.getItem(FORM_DATA_KEY) !== null;
  } catch {
    return false;
  }
}

/**
 * Auto-save form data with debouncing
 */
export function createAutoSave(
  formData: ServiceFormData, 
  serviceType?: string,
  debounceMs: number = 1000
): () => void {
  let timeoutId: NodeJS.Timeout;
  
  return () => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      saveFormData(formData, serviceType);
    }, debounceMs);
  };
}

/**
 * Merge saved form data with current form data
 */
export function mergeFormData(
  currentData: ServiceFormData, 
  savedData: ServiceFormData
): ServiceFormData {
  return {
    ...currentData,
    ...savedData,
    // Preserve nested objects
    propertyDetails: {
      ...currentData.propertyDetails,
      ...savedData.propertyDetails
    },
    contact: {
      ...currentData.contact,
      ...savedData.contact
    },
    schedule: {
      ...currentData.schedule,
      ...savedData.schedule
    }
  };
}
